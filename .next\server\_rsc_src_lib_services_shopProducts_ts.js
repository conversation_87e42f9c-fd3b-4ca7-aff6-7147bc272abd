"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_services_shopProducts_ts";
exports.ids = ["_rsc_src_lib_services_shopProducts_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/services/shopProducts.ts":
/*!******************************************!*\
  !*** ./src/lib/services/shopProducts.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ShopProductService: () => (/* binding */ ShopProductService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ShopProductService - Service for managing independent shop products\n * Products are completely separate from the ads system\n */ class ShopProductService {\n    /**\n   * Create a new shop product\n   */ static async createProduct(productData, imageUrls) {\n        try {\n            // Create the product\n            const { data: product, error: productError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).insert({\n                ...productData,\n                currency: productData.currency || \"LKR\",\n                condition: productData.condition || \"new\",\n                min_order_quantity: productData.min_order_quantity || 1,\n                is_digital: productData.is_digital || false,\n                negotiable: productData.negotiable || false,\n                status: productData.status || \"active\"\n            }).select(`\n          *,\n          shop:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS}(*),\n          category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES}(*),\n          subcategory:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES}(*)\n        `).single();\n            if (productError) {\n                console.error(\"Error creating product:\", productError);\n                throw new Error(`Failed to create product: ${productError.message}`);\n            }\n            // Add images if provided\n            if (imageUrls && imageUrls.length > 0) {\n                const imageInserts = imageUrls.map((url, index)=>({\n                        product_id: product.id,\n                        image_url: url,\n                        sort_order: index,\n                        is_primary: index === 0\n                    }));\n                const { error: imageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).insert(imageInserts);\n                if (imageError) {\n                    console.error(\"Error adding product images:\", imageError);\n                // Don't throw here, product is created successfully\n                }\n            }\n            return product;\n        } catch (error) {\n            console.error(\"Error in createProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get products for a shop\n   */ static async getShopProducts(shopId, page = 1, limit = 20, filters) {\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(`\n          *,\n          shop:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS}(*),\n          category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES}(*),\n          subcategory:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES}(*),\n          images:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES}(*)\n        `, {\n                count: \"exact\"\n            }).eq(\"shop_id\", shopId);\n            // Apply filters\n            if (filters?.category_id) {\n                query = query.eq(\"category_id\", filters.category_id);\n            }\n            if (filters?.subcategory_id) {\n                query = query.eq(\"subcategory_id\", filters.subcategory_id);\n            }\n            if (filters?.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters?.search) {\n                query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);\n            }\n            const { data, error, count } = await query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching shop products:\", error);\n                throw new Error(`Failed to fetch products: ${error.message}`);\n            }\n            // Return products without refreshing statistics to improve performance\n            // Statistics will be refreshed only when needed (e.g., when viewing individual products)\n            return {\n                products: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error in getShopProducts:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all products across all shops with filters\n   */ static async getAllProducts(page = 1, limit = 20, filters) {\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(`\n          *,\n          shop:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS}(*),\n          category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES}(*),\n          subcategory:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES}(*),\n          images:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES}(*)\n        `, {\n                count: \"exact\"\n            });\n            // Only show products from approved shops with active status\n            query = query.eq(\"shop.status\", \"approved\").eq(\"status\", \"active\");\n            // Apply filters\n            if (filters?.category_id) {\n                query = query.eq(\"category_id\", filters.category_id);\n            }\n            if (filters?.subcategory_id) {\n                query = query.eq(\"subcategory_id\", filters.subcategory_id);\n            }\n            if (filters?.shop_id) {\n                query = query.eq(\"shop_id\", filters.shop_id);\n            }\n            if (filters?.search) {\n                query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);\n            }\n            if (filters?.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            const { data, error, count } = await query.order(\"created_at\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (error) {\n                console.error(\"Error fetching all products:\", error);\n                throw new Error(`Failed to fetch products: ${error.message}`);\n            }\n            // Return products without refreshing statistics to improve performance\n            return {\n                products: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"Error in getAllProducts:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update a product\n   */ static async updateProduct(id, productData) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update(productData).eq(\"id\", id).select(`\n          *,\n          shop:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS}(*),\n          category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES}(*),\n          subcategory:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES}(*),\n          images:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES}(*)\n        `).single();\n            if (error) {\n                console.error(\"Error updating product:\", error);\n                throw new Error(`Failed to update product: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error in updateProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Delete a product\n   */ static async deleteProduct(id) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).delete().eq(\"id\", id);\n            if (error) {\n                console.error(\"Error deleting product:\", error);\n                throw new Error(`Failed to delete product: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error in deleteProduct:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Refresh product review statistics\n   */ static async refreshProductReviewStats(productId) {\n        try {\n            // Get review statistics\n            const { data: reviewStats } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.PRODUCT_REVIEWS).select(\"rating\").eq(\"product_id\", productId);\n            const reviews = reviewStats || [];\n            const totalReviews = reviews.length;\n            const averageRating = totalReviews > 0 ? reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n            // Update product with calculated statistics\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update({\n                total_reviews: totalReviews,\n                average_rating: Math.round(averageRating * 100) / 100\n            }).eq(\"id\", productId);\n        } catch (error) {\n            console.error(\"Error refreshing product review stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Increment product view count\n   */ static async incrementProductViews(productId) {\n        try {\n            // First get current views count\n            const { data: currentProduct } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(\"views\").eq(\"id\", productId).single();\n            const currentViews = currentProduct?.views || 0;\n            // Update with incremented views\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).update({\n                views: currentViews + 1\n            }).eq(\"id\", productId);\n            if (error) {\n                console.error(\"Error incrementing product views:\", error);\n            // Don't throw error for view counting failures\n            }\n        } catch (error) {\n            console.error(\"Error in incrementProductViews:\", error);\n        // Don't throw error for view counting failures\n        }\n    }\n    /**\n   * Get product by ID with review statistics\n   */ static async getProductById(id) {\n        try {\n            // First refresh the review statistics to ensure they're current\n            await this.refreshProductReviewStats(id);\n            // Increment view count\n            await this.incrementProductViews(id);\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCTS).select(`\n          *,\n          shop:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.VENDOR_SHOPS}(*),\n          category:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_CATEGORIES}(*),\n          subcategory:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_SUBCATEGORIES}(*),\n          images:${_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES}(*)\n        `).eq(\"id\", id).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Not found\n                    ;\n                }\n                throw new Error(`Failed to fetch product: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error in getProductById:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Add images to a product\n   */ static async addProductImages(productId, imageUrls) {\n        try {\n            const imageInserts = imageUrls.map((url, index)=>({\n                    product_id: productId,\n                    image_url: url,\n                    sort_order: index,\n                    is_primary: index === 0\n                }));\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).insert(imageInserts).select();\n            if (error) {\n                console.error(\"Error adding product images:\", error);\n                throw new Error(`Failed to add images: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error in addProductImages:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Remove product image\n   */ static async removeProductImage(imageId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.SHOP_PRODUCT_IMAGES).delete().eq(\"id\", imageId);\n            if (error) {\n                console.error(\"Error removing product image:\", error);\n                throw new Error(`Failed to remove image: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error in removeProductImage:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/shopProducts.ts\n");

/***/ })

};
;