import { NextRequest, NextResponse } from 'next/server'
import { VendorShopService } from '@/lib/services/vendorShops'

export async function POST(request: NextRequest) {
  try {
    // Refresh all shop statistics
    await VendorShopService.refreshAllShopStatistics()

    return NextResponse.json({
      success: true,
      message: 'Shop statistics refreshed successfully'
    })
  } catch (error) {
    console.error('Error refreshing shop statistics:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to refresh shop statistics'
      },
      { status: 500 }
    )
  }
}
