"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/refresh-shop-stats/route";
exports.ids = ["app/api/admin/refresh-shop-stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&page=%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&page=%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_refresh_shop_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/refresh-shop-stats/route.ts */ \"(rsc)/./src/app/api/admin/refresh-shop-stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/refresh-shop-stats/route\",\n        pathname: \"/api/admin/refresh-shop-stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/refresh-shop-stats/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\refresh-shop-stats\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_refresh_shop_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/refresh-shop-stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&page=%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/refresh-shop-stats/route.ts":
/*!*******************************************************!*\
  !*** ./src/app/api/admin/refresh-shop-stats/route.ts ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Create admin Supabase client with service role key\nconst supabaseAdmin = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\nasync function POST(request) {\n    try {\n        const body = await request.json().catch(()=>({}));\n        const { shopId } = body;\n        if (shopId) {\n            // Refresh specific shop statistics using admin client\n            console.log(`Refreshing statistics for specific shop: ${shopId}`);\n            const result = await refreshShopStatisticsAdmin(shopId);\n            console.log(\"Shop refresh result:\", result);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: `Shop statistics refreshed successfully for shop: ${shopId}`,\n                data: result\n            });\n        } else {\n            // Refresh all shop statistics using admin client\n            console.log(\"Refreshing statistics for all shops\");\n            await refreshAllShopStatisticsAdmin();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: \"Shop statistics refreshed successfully for all shops\"\n            });\n        }\n    } catch (error) {\n        console.error(\"Error refreshing shop statistics:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to refresh shop statistics\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Admin version of refreshShopStatistics that uses service role\nasync function refreshShopStatisticsAdmin(shopId) {\n    try {\n        console.log(`Refreshing statistics for shop: ${shopId}`);\n        // First get the product IDs for this shop\n        const { data: shopProducts, error: productsError } = await supabaseAdmin.from(\"shop_products\").select(\"id\").eq(\"shop_id\", shopId);\n        if (productsError) {\n            console.error(\"Error fetching shop products:\", productsError);\n            throw new Error(`Failed to fetch shop products: ${productsError.message}`);\n        }\n        const productIds = shopProducts?.map((p)=>p.id) || [];\n        // Get current statistics from related tables with individual error handling\n        const [productsResult, reviewsResult, ordersResult, viewsResult] = await Promise.allSettled([\n            // Count products\n            supabaseAdmin.from(\"shop_products\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"shop_id\", shopId),\n            // Get product reviews for rating calculation\n            productIds.length > 0 ? supabaseAdmin.from(\"product_reviews\").select(\"rating\").in(\"product_id\", productIds) : Promise.resolve({\n                data: [],\n                error: null\n            }),\n            // Count completed sales\n            supabaseAdmin.from(\"shop_orders\").select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"shop_id\", shopId).in(\"status\", [\n                \"delivered\",\n                \"completed\"\n            ]),\n            // Calculate total views from all products\n            supabaseAdmin.from(\"shop_products\").select(\"views\").eq(\"shop_id\", shopId)\n        ]);\n        // Process results with error handling\n        const totalProducts = productsResult.status === \"fulfilled\" && !productsResult.value.error ? productsResult.value.count || 0 : 0;\n        const reviews = reviewsResult.status === \"fulfilled\" && !reviewsResult.value.error ? reviewsResult.value.data || [] : [];\n        const totalReviews = reviews.length;\n        const averageRating = totalReviews > 0 ? reviews.reduce((sum, review)=>sum + review.rating, 0) / totalReviews : 0;\n        const totalSales = ordersResult.status === \"fulfilled\" && !ordersResult.value.error ? ordersResult.value.count || 0 : 0;\n        const productViews = viewsResult.status === \"fulfilled\" && !viewsResult.value.error ? viewsResult.value.data || [] : [];\n        const totalViews = productViews.reduce((sum, product)=>sum + (product.views || 0), 0);\n        console.log(`Shop ${shopId} statistics:`, {\n            totalProducts,\n            totalReviews,\n            averageRating,\n            totalSales,\n            totalViews\n        });\n        // Update the shop with calculated statistics using admin client\n        const { data, error } = await supabaseAdmin.from(\"vendor_shops\").update({\n            total_products: totalProducts,\n            total_reviews: totalReviews,\n            rating: Number(averageRating.toFixed(2)),\n            total_sales: totalSales,\n            total_views: totalViews,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", shopId).select();\n        if (error) {\n            throw new Error(`Failed to update shop statistics: ${error.message}`);\n        }\n        if (!data || data.length === 0) {\n            throw new Error(`Shop not found: ${shopId}`);\n        }\n        console.log(`Successfully updated statistics for shop: ${shopId}`);\n        return data[0];\n    } catch (error) {\n        console.error(\"Error refreshing shop statistics:\", error);\n        throw error;\n    }\n}\n// Admin version of refreshAllShopStatistics that uses service role\nasync function refreshAllShopStatisticsAdmin() {\n    try {\n        console.log(\"Refreshing statistics for all shops...\");\n        // Get all approved shops\n        const { data: shops, error } = await supabaseAdmin.from(\"vendor_shops\").select(\"id, name\").eq(\"status\", \"approved\");\n        if (error) {\n            throw new Error(`Failed to fetch shops: ${error.message}`);\n        }\n        if (!shops || shops.length === 0) {\n            console.log(\"No approved shops found\");\n            return;\n        }\n        // Refresh statistics for each shop\n        for (const shop of shops){\n            try {\n                await refreshShopStatisticsAdmin(shop.id);\n                console.log(`✓ Updated statistics for shop: ${shop.name}`);\n            } catch (error) {\n                console.error(`✗ Failed to update statistics for shop ${shop.name}:`, error);\n            }\n        }\n        console.log(\"Completed refreshing statistics for all shops\");\n    } catch (error) {\n        console.error(\"Error refreshing all shop statistics:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/refresh-shop-stats/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&page=%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Frefresh-shop-stats%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();